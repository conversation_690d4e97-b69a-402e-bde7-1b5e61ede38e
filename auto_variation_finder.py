#!/usr/bin/env python3
"""
Automatic Image Variation Finder and Combiner

A Python tool that automatically finds similar image variations and combines them
into batches of 4 images per composite.
"""

import os
import sys
from PIL import Image
import argparse
from typing import List, Tuple, Dict, Set, Optional
from collections import defaultdict
import glob
from image_combiner import ImageCombiner
import shutil
import numpy as np
try:
    import torch
    import clip
    from sklearn.metrics.pairwise import cosine_similarity
    CLIP_AVAILABLE = True
except ImportError:
    CLIP_AVAILABLE = False
    print("Error: CLIP not available. Install with: pip install torch torchvision clip-by-openai scikit-learn")
    print("CLIP is required for this application.")
    sys.exit(1)

class ImageVariationFinder:
    def __init__(self, clip_threshold: float = 0.85, clip_model_name: str = "ViT-B/32", device: str = "auto"):
        """
        Initialize the variation finder.
        
        Args:
            clip_threshold: Minimum cosine similarity for CLIP (0.0-1.0, higher = more strict)
            clip_model_name: CLIP model to use ("ViT-B/32", "ViT-L/14", "RN50", etc.)
            device: Device to use ("auto", "cpu", "gpu", "mps", "cuda")
        """
        self.clip_threshold = clip_threshold
        self.clip_model_name = clip_model_name
        self.image_features = {}
        self.image_paths = []
        
        # Initialize CLIP model
        if not CLIP_AVAILABLE:
            raise RuntimeError("CLIP is required but not available. Install with: pip install torch torchvision clip-by-openai scikit-learn")
        
        try:
            print(f"Loading CLIP model: {self.clip_model_name}...")
            self.device = self._select_device(device)
            self.clip_model, self.clip_preprocess = clip.load(self.clip_model_name, device=self.device)
            print(f"CLIP model {self.clip_model_name} loaded on {self.device}")
        except Exception as e:
            raise RuntimeError(f"Failed to load CLIP model: {e}")
    
    def _select_device(self, device_preference: str) -> str:
        """
        Select the appropriate device based on preference and availability.
        
        Args:
            device_preference: User's device preference
            
        Returns:
            str: Selected device name
        """
        if device_preference == "cpu":
            return "cpu"
        elif device_preference == "auto":
            # Auto-select best available device
            if torch.backends.mps.is_available():
                return "mps"
            elif torch.cuda.is_available():
                return "cuda"
            else:
                return "cpu"
        elif device_preference in ["gpu", "mps"]:
            if torch.backends.mps.is_available():
                return "mps"
            else:
                print("Warning: MPS not available, falling back to CPU")
                return "cpu"
        elif device_preference == "cuda":
            if torch.cuda.is_available():
                return "cuda"
            else:
                print("Warning: CUDA not available, falling back to CPU")
                return "cpu"
        else:
            print(f"Warning: Unknown device '{device_preference}', using auto-selection")
            return self._select_device("auto")
    

    
    def extract_clip_features(self, image_path: str) -> np.ndarray:
        """
        Extract CLIP features for an image.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            CLIP feature vector as numpy array
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Preprocess image for CLIP
                image_input = self.clip_preprocess(img).unsqueeze(0).to(self.device)
                
                # Extract features
                with torch.no_grad():
                    image_features = self.clip_model.encode_image(image_input)
                    # Normalize features
                    image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                
                return image_features.cpu().numpy().flatten()
        except Exception as e:
            print(f"Error extracting CLIP features from {image_path}: {e}")
            return None
    
    def scan_directory(self, directory: str, extensions: List[str] = None) -> List[str]:
        """
        Scan directory for image files.
        
        Args:
            directory: Directory to scan
            extensions: List of file extensions to include
            
        Returns:
            List of image file paths
        """
        if extensions is None:
            extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp']
        
        image_files = []
        for ext in extensions:
            pattern = os.path.join(directory, f"**/*{ext}")
            image_files.extend(glob.glob(pattern, recursive=True))
            pattern = os.path.join(directory, f"**/*{ext.upper()}")
            image_files.extend(glob.glob(pattern, recursive=True))
        
        return sorted(list(set(image_files)))
    
    def find_similar_images(self, image_paths: List[str]) -> Dict[str, List[str]]:
        """
        Find groups of similar images using CLIP.
        
        Args:
            image_paths: List of image file paths
            
        Returns:
            Dictionary mapping representative image to list of similar images
        """
        print(f"Analyzing {len(image_paths)} images for similarity...")
        return self._find_similar_images_clip(image_paths)
    
    def _find_similar_images_clip(self, image_paths: List[str]) -> Dict[str, List[str]]:
        """
        Find similar images using CLIP features with improved clustering algorithm.
        """
        print("Using CLIP for similarity detection...")

        # Extract CLIP features for all images
        valid_images = []
        feature_matrix = []

        for i, path in enumerate(image_paths):
            print(f"Processing image {i+1}/{len(image_paths)}: {os.path.basename(path)}")
            features = self.extract_clip_features(path)
            if features is not None:
                self.image_features[path] = features
                valid_images.append(path)
                feature_matrix.append(features)
            else:
                print(f"Skipping invalid image: {path}")

        if len(valid_images) < 2:
            print("Not enough valid images for similarity comparison")
            return {}

        print(f"Successfully processed {len(valid_images)} images")

        # Convert to numpy array for efficient computation
        feature_matrix = np.array(feature_matrix)

        # Calculate cosine similarity matrix
        print("Calculating similarity matrix...")
        similarity_matrix = cosine_similarity(feature_matrix)

        # Store for use in intelligent batch splitting
        self._current_similarity_matrix = similarity_matrix
        self.image_paths = valid_images

        # Use improved clustering algorithm
        groups = self._cluster_similar_images(valid_images, similarity_matrix)

        print(f"Total groups created: {len(groups)}")
        return groups

    def _cluster_similar_images(self, valid_images: List[str], similarity_matrix: np.ndarray) -> Dict[str, List[str]]:
        """
        Improved clustering algorithm that creates more accurate similarity groups.
        Uses connected components approach to ensure all images in a group are similar to each other.

        Args:
            valid_images: List of valid image paths
            similarity_matrix: Precomputed similarity matrix

        Returns:
            Dictionary mapping representative image paths to lists of similar images
        """
        print(f"Starting improved clustering with threshold: {self.clip_threshold}")

        # Create adjacency matrix for similarity graph
        n = len(valid_images)
        adjacency = np.zeros((n, n), dtype=bool)

        # Build adjacency matrix based on similarity threshold
        for i in range(n):
            for j in range(i + 1, n):
                if similarity_matrix[i][j] >= self.clip_threshold:
                    adjacency[i][j] = True
                    adjacency[j][i] = True

        # Find connected components using DFS
        visited = [False] * n
        groups = {}

        def dfs(node, component):
            """Depth-first search to find connected component"""
            visited[node] = True
            component.append(node)

            for neighbor in range(n):
                if adjacency[node][neighbor] and not visited[neighbor]:
                    dfs(neighbor, component)

        # Find all connected components
        for i in range(n):
            if not visited[i]:
                component = []
                dfs(i, component)

                if len(component) > 1:
                    # Find the most representative image (highest average similarity to others in group)
                    best_representative = None
                    best_avg_similarity = -1

                    for candidate_idx in component:
                        # Calculate average similarity to other images in the group
                        similarities = [similarity_matrix[candidate_idx][other_idx]
                                      for other_idx in component if other_idx != candidate_idx]
                        avg_similarity = np.mean(similarities) if similarities else 0

                        if avg_similarity > best_avg_similarity:
                            best_avg_similarity = avg_similarity
                            best_representative = candidate_idx

                    # Create group with representative as key
                    representative_path = valid_images[best_representative]
                    group_paths = [valid_images[idx] for idx in component]
                    groups[representative_path] = group_paths

                    print(f"Found group of {len(component)} similar images (avg similarity: {best_avg_similarity:.3f}, representative: {os.path.basename(representative_path)})")

                elif len(component) == 1:
                    # Single image group
                    single_path = valid_images[component[0]]
                    groups[single_path] = [single_path]
                    print(f"Created single-image group for: {os.path.basename(single_path)}")

        return groups
    

    
    def create_batches(self, similar_groups: Dict[str, List[str]], batch_size: int = 4) -> List[List[str]]:
        """
        Create batches of images from similar groups based on similarity.
        Each batch contains similar images with a maximum of batch_size images.
        Uses intelligent splitting to maintain similarity within split batches.

        Args:
            similar_groups: Dictionary of similar image groups
            batch_size: Maximum number of images per batch (used as limit, not target)

        Returns:
            List of batches, each containing similar images (max batch_size per batch)
        """
        batches = []

        print(f"Creating batches from {len(similar_groups)} groups with max batch size {batch_size}")
        total_images = sum(len(group) for group in similar_groups.values())
        print(f"Total images to be processed: {total_images}")

        for representative, group in similar_groups.items():
            # If group has more than batch_size images, split intelligently
            if len(group) > batch_size:
                # Use intelligent splitting that maintains similarity within batches
                split_batches = self._split_group_intelligently(group, batch_size)
                batches.extend(split_batches)
                print(f"Split large group into {len(split_batches)} batches of similar images")
            else:
                # Use the entire group as one batch
                # Include single-image groups as well to ensure all images are processed
                batches.append(group)
                if len(group) == 1:
                    print(f"Created batch {len(batches)} with 1 image (will be preserved as-is)")
                else:
                    print(f"Created batch {len(batches)} with {len(group)} images")

        total_batched_images = sum(len(batch) for batch in batches)
        print(f"Total images in batches: {total_batched_images}")
        print(f"Created {len(batches)} batches total")

        # Validate batches for similarity consistency
        validated_batches = self._validate_batch_similarity(batches)

        return validated_batches

    def _split_group_intelligently(self, group: List[str], batch_size: int) -> List[List[str]]:
        """
        Split a large group into smaller batches while maintaining similarity within each batch.
        Uses hierarchical clustering approach to ensure similar images stay together.

        Args:
            group: List of image paths in the group
            batch_size: Maximum size for each batch

        Returns:
            List of batches, each containing similar images
        """
        if len(group) <= batch_size:
            return [group]

        # Get similarity matrix for this group
        if not hasattr(self, '_current_similarity_matrix') or not hasattr(self, 'image_paths'):
            print(f"Similarity matrix not available, falling back to sequential splitting for group of {len(group)} images")
            return [group[i:i + batch_size] for i in range(0, len(group), batch_size)]

        group_indices = []
        for img_path in group:
            if img_path in self.image_paths:
                group_indices.append(self.image_paths.index(img_path))

        # If we don't have all indices, fall back to sequential splitting
        if len(group_indices) != len(group):
            print(f"Missing indices for some images, falling back to sequential splitting for group of {len(group)} images")
            return [group[i:i + batch_size] for i in range(0, len(group), batch_size)]

        # Extract sub-similarity matrix for this group
        n = len(group)
        group_similarity = np.zeros((n, n))

        for i, idx1 in enumerate(group_indices):
            for j, idx2 in enumerate(group_indices):
                if i < len(group) and j < len(group):
                    group_similarity[i][j] = self._current_similarity_matrix[idx1][idx2]

        # Use hierarchical clustering to split the group
        try:
            from scipy.cluster.hierarchy import linkage, fcluster
            from scipy.spatial.distance import squareform

            # Convert similarity to distance (1 - similarity)
            distance_matrix = 1 - group_similarity

            # Make sure diagonal is 0 (distance from image to itself)
            np.fill_diagonal(distance_matrix, 0)

            # Convert to condensed distance matrix for linkage
            condensed_distances = squareform(distance_matrix, checks=False)

            # Perform hierarchical clustering
            linkage_matrix = linkage(condensed_distances, method='average')

            # Determine number of clusters needed
            num_clusters = math.ceil(len(group) / batch_size)

            # Get cluster assignments
            cluster_labels = fcluster(linkage_matrix, num_clusters, criterion='maxclust')

            # Group images by cluster
            clusters = defaultdict(list)
            for i, label in enumerate(cluster_labels):
                if i < len(group):
                    clusters[label].append(group[i])

            # Convert clusters to batches, splitting if any cluster is too large
            batches = []
            for cluster_images in clusters.values():
                if len(cluster_images) <= batch_size:
                    batches.append(cluster_images)
                else:
                    # If cluster is still too large, split sequentially
                    for i in range(0, len(cluster_images), batch_size):
                        batches.append(cluster_images[i:i + batch_size])

            print(f"Intelligently split group of {len(group)} into {len(batches)} similarity-based batches")
            return batches

        except ImportError:
            print("SciPy not available, falling back to sequential splitting")
            return [group[i:i + batch_size] for i in range(0, len(group), batch_size)]
        except Exception as e:
            print(f"Error in intelligent splitting: {e}, falling back to sequential splitting")
            return [group[i:i + batch_size] for i in range(0, len(group), batch_size)]

    def _validate_batch_similarity(self, batches: List[List[str]]) -> List[List[str]]:
        """
        Validate that all images in each batch meet minimum similarity requirements.
        Remove or reorganize images that don't meet the threshold.

        Args:
            batches: List of image batches to validate

        Returns:
            List of validated batches with consistent similarity
        """
        if not hasattr(self, '_current_similarity_matrix') or not hasattr(self, 'image_paths'):
            print("Similarity matrix not available, skipping batch validation")
            return batches

        validated_batches = []
        removed_images = []

        print(f"Validating similarity consistency for {len(batches)} batches...")

        for batch_idx, batch in enumerate(batches):
            if len(batch) <= 1:
                # Single image batches are always valid
                validated_batches.append(batch)
                continue

            # Get indices for this batch
            batch_indices = []
            valid_batch_images = []

            for img_path in batch:
                if img_path in self.image_paths:
                    batch_indices.append(self.image_paths.index(img_path))
                    valid_batch_images.append(img_path)

            if len(batch_indices) <= 1:
                validated_batches.append(valid_batch_images)
                continue

            # Calculate average pairwise similarity for this batch
            similarities = []
            for i in range(len(batch_indices)):
                for j in range(i + 1, len(batch_indices)):
                    idx1, idx2 = batch_indices[i], batch_indices[j]
                    similarity = self._current_similarity_matrix[idx1][idx2]
                    similarities.append(similarity)

            avg_similarity = np.mean(similarities) if similarities else 0
            min_similarity = np.min(similarities) if similarities else 0

            # Check if batch meets quality standards
            similarity_threshold = self.clip_threshold * 0.9  # Slightly lower threshold for batch validation

            if min_similarity >= similarity_threshold:
                # Batch is good as-is
                validated_batches.append(valid_batch_images)
                print(f"Batch {batch_idx + 1}: VALID (avg: {avg_similarity:.3f}, min: {min_similarity:.3f})")
            else:
                # Need to filter out problematic images
                print(f"Batch {batch_idx + 1}: NEEDS FILTERING (avg: {avg_similarity:.3f}, min: {min_similarity:.3f})")

                # Find images that don't meet similarity requirements
                good_images = []
                bad_images = []

                for i, img_path in enumerate(valid_batch_images):
                    # Check this image's similarity to all others in the batch
                    img_similarities = []
                    for j, other_img in enumerate(valid_batch_images):
                        if i != j:
                            idx1, idx2 = batch_indices[i], batch_indices[j]
                            img_similarities.append(self._current_similarity_matrix[idx1][idx2])

                    avg_img_similarity = np.mean(img_similarities) if img_similarities else 1.0

                    if avg_img_similarity >= similarity_threshold:
                        good_images.append(img_path)
                    else:
                        bad_images.append(img_path)
                        print(f"  Removing {os.path.basename(img_path)} (avg similarity: {avg_img_similarity:.3f})")

                if len(good_images) > 0:
                    validated_batches.append(good_images)
                    print(f"  Kept {len(good_images)} images in batch")

                # Add problematic images to removed list for potential reprocessing
                removed_images.extend(bad_images)

        # Try to create new batches from removed images
        if removed_images:
            print(f"Attempting to reprocess {len(removed_images)} removed images...")
            # Create single-image batches for removed images
            for img_path in removed_images:
                validated_batches.append([img_path])
                print(f"Created single-image batch for: {os.path.basename(img_path)}")

        print(f"Validation complete: {len(validated_batches)} validated batches")
        return validated_batches
    
    def process_directory(self, input_dir: str, output_dir: str, batch_size: int = 4, 
                         target_size: Optional[Tuple[int, int]] = (300, 300), layout_mode: str = "grid", 
                         spacing: int = 15, background_color: str = "white", quality: int = 95,
                         clip_threshold: float = None, generate_master_file: bool = False):
        """
        Process a directory to find variations and create combined images.
        
        Args:
            input_dir: Input directory containing images
            output_dir: Output directory for combined images
            batch_size: Number of images per batch
            target_size: Target size for individual images (width, height), or None for dynamic sizing
            layout_mode: Layout mode ('grid', 'horizontal', 'vertical')
            spacing: Spacing between images in pixels
            background_color: Background color for combined images
            quality: JPEG quality (70-100)
            clip_threshold: Override clip_threshold for this operation
            generate_master_file: Whether to generate a master text file with all batch info
        """
        # Override settings if provided
        if clip_threshold is not None:
            original_clip_threshold = self.clip_threshold
            self.clip_threshold = clip_threshold
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Scan for images
        print(f"Scanning directory: {input_dir}")
        image_paths = self.scan_directory(input_dir)
        
        if not image_paths:
            print("No images found in the specified directory")
            return []
        
        print(f"Found {len(image_paths)} image files")
        
        # Track all processed images to handle unprocessed ones later
        all_processed_images = set()
        
        # Find similar images
        similar_groups = self.find_similar_images(image_paths)
        
        if not similar_groups:
            print("No similar image groups found")
            # Don't return yet, process individual images below
        else:
            print(f"Found {len(similar_groups)} groups of similar images")
        
        # Create batches
        batches = self.create_batches(similar_groups, batch_size)
        
        if not batches:
            print("No valid batches created")
            # Don't return yet, process individual images below
        else:
            print(f"Created {len(batches)} batches for processing")
        
        # Combine images in each batch
        combiner = ImageCombiner(spacing=spacing, background_color=background_color)
        
        batch_counter = 0
        
        for i, batch in enumerate(batches, 1):
            try:
                print(f"\nProcessing batch {i}/{len(batches)} ({len(batch)} images)...")
                
                # Add all images in this batch to the processed set
                for img_path in batch:
                    all_processed_images.add(img_path)
                
                # For single-image batches, preserve the image as-is without combining
                if len(batch) == 1:
                    img_path = batch[0]
                    batch_counter += 1
                    
                    # Save image with original format, maintaining exact same image and size
                    original_format = os.path.splitext(img_path)[1].lower()
                    if original_format in ['.png', '.gif', '.webp', '.tiff', '.tif'] or background_color.lower() == "transparent":
                        output_filename = f"variation_batch_{batch_counter:03d}{original_format}"
                    else:
                        output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Copy the original file directly to preserve the exact image with its original size
                    shutil.copy2(img_path, output_path)
                    
                    print(f"Saved individual image as-is: {output_filename} (original size preserved)")
                    
                    # Create a text file listing the source image
                    info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                    info_path = os.path.join(output_dir, info_filename)
                    with open(info_path, 'w') as f:
                        f.write(f"Batch {batch_counter} - Source Image:\n")
                        f.write("=" * 30 + "\n")
                        f.write(f"1. {os.path.relpath(img_path, input_dir)}\n")
                    
                    continue  # Skip the rest of the loop for single images
                
                # For multi-image batches, combine them as before
                # Load images
                images = combiner.load_images(batch)
                
                if not images:
                    print(f"Failed to load images for batch {i}")
                    continue
                
                # Resize to uniform size
                images = combiner.resize_images_uniform(images, target_size=target_size)
                
                # Combine images based on layout mode
                if layout_mode == "grid":
                    combined = combiner.combine_grid(images)  # Let it auto-determine optimal layout
                elif layout_mode == "horizontal":
                    combined = combiner.combine_horizontal(images)
                elif layout_mode == "vertical":
                    combined = combiner.combine_vertical(images)
                else:
                    combined = combiner.combine_grid(images)  # Default to grid with optimal layout
                
                # Save combined image
                # Use PNG for transparent backgrounds, JPG for others
                batch_counter += 1
                if background_color.lower() == "transparent":
                    output_filename = f"variation_batch_{batch_counter:03d}.png"
                    output_path = os.path.join(output_dir, output_filename)
                    combined.save(output_path)
                else:
                    output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Convert RGBA to RGB if needed for JPEG format
                    if combined.mode == 'RGBA':
                        # Create a white background and paste the RGBA image onto it
                        rgb_image = Image.new('RGB', combined.size, (255, 255, 255))
                        rgb_image.paste(combined, mask=combined.split()[-1])  # Use alpha channel as mask
                        rgb_image.save(output_path, quality=quality)
                    else:
                        combined.save(output_path, quality=quality)
                
                print(f"Saved: {output_filename} ({combined.size[0]}x{combined.size[1]})")
                
                # Create a text file listing the source images
                info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                info_path = os.path.join(output_dir, info_filename)
                with open(info_path, 'w') as f:
                    f.write(f"Batch {batch_counter} - Source Images:\n")
                    f.write("=" * 30 + "\n")
                    for j, img_path in enumerate(batch, 1):
                        f.write(f"{j}. {os.path.relpath(img_path, input_dir)}\n")
                
            except Exception as e:
                print(f"Error processing batch {i}: {e}")
                continue
        
        # Process any unprocessed images individually (no similarity match)
        unprocessed = [path for path in image_paths if path not in all_processed_images]
        if unprocessed:
            print(f"\nProcessing {len(unprocessed)} remaining images with no similarity matches...")
            
            # Process each unprocessed image individually
            for i, img_path in enumerate(unprocessed):
                try:
                    # Create a batch with just this one image
                    single_batch = [img_path]
                    
                    # Since it's a single image with no matches, preserve it exactly as is
                    batch_counter += 1
                    
                    # Save image with original format, maintaining exact same image and size
                    original_format = os.path.splitext(img_path)[1].lower()
                    if original_format in ['.png', '.gif', '.webp', '.tiff', '.tif'] or background_color.lower() == "transparent":
                        output_filename = f"variation_batch_{batch_counter:03d}{original_format}"
                    else:
                        output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Copy the original file directly to preserve the exact image with its original size
                    shutil.copy2(img_path, output_path)
                    
                    print(f"Saved individual image as-is: {output_filename} (original size preserved)")
                    
                    # Create a text file listing the source image
                    info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                    info_path = os.path.join(output_dir, info_filename)
                    with open(info_path, 'w') as f:
                        f.write(f"Batch {batch_counter} - Source Image:\n")
                        f.write("=" * 30 + "\n")
                        f.write(f"1. {os.path.relpath(img_path, input_dir)}\n")
                        
                except Exception as e:
                    print(f"Error processing individual image {img_path}: {e}")
                    continue
        
        # Generate a master text file with all batch information if requested
        if generate_master_file:
            master_filename = "all_batches_info.txt"
            master_path = os.path.join(output_dir, master_filename)
            with open(master_path, 'w') as f:
                f.write(f"Image Variation Batches - Master File\n")
                f.write(f"Total images processed: {len(image_paths)}\n")
                f.write(f"Total batches created: {batch_counter}\n")
                f.write(f"Similarity threshold: {self.clip_threshold}\n")
                f.write(f"CLIP model: {self.clip_model_name}\n")
                f.write("=" * 60 + "\n\n")
                
                # Write info about each batch
                for i in range(1, batch_counter + 1):
                    batch_file_path = os.path.join(output_dir, f"variation_batch_{i:03d}_sources.txt")
                    if os.path.exists(batch_file_path):
                        f.write(f"Batch #{i:03d}\n")
                        f.write("-" * 30 + "\n")
                        
                        # Copy content from the individual batch file
                        with open(batch_file_path, 'r') as batch_file:
                            # Skip the first two lines (header)
                            next(batch_file)  # Skip "Batch X - Source Images:"
                            next(batch_file)  # Skip "===================="
                            
                            # Copy the rest of the file
                            for line in batch_file:
                                f.write(line)
                        
                        f.write("\n")
            
            print(f"Generated master text file: {master_filename}")
        
        # Restore original settings if they were overridden
        if clip_threshold is not None:
            self.clip_threshold = original_clip_threshold
            
        print(f"\nProcessing complete! Check the output directory: {output_dir}")
        print(f"Total batches created: {batch_counter}")
        return batches

def main():
    parser = argparse.ArgumentParser(description="Automatically find image variations and combine them into batches using CLIP")
    parser.add_argument("input_dir", help="Input directory containing images")
    parser.add_argument("-o", "--output", default="variation_batches", help="Output directory for combined images")
    parser.add_argument("-b", "--batch-size", type=int, default=4, help="Number of images per batch")
    parser.add_argument("--clip-threshold", type=float, default=0.85, help="CLIP similarity threshold (0.0-1.0, higher = more strict)")
    parser.add_argument("--clip-model", default="ViT-B/32", help="CLIP model to use (ViT-B/32, ViT-L/14, RN50, etc.)")
    parser.add_argument("--device", default="auto", choices=["auto", "cpu", "gpu", "mps", "cuda"], help="Device to use for processing (auto, cpu, gpu, mps, cuda)")
    parser.add_argument("--extensions", nargs="+", help="File extensions to include (e.g., .jpg .png)")
    parser.add_argument("--master-file", action="store_true", help="Generate a master text file with all batch information")
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.input_dir):
        print(f"Error: Input directory '{args.input_dir}' does not exist")
        sys.exit(1)
    
    # Create variation finder
    finder = ImageVariationFinder(
        clip_threshold=args.clip_threshold,
        clip_model_name=args.clip_model,
        device=args.device
    )
    
    # Process directory
    finder.process_directory(
        input_dir=args.input_dir,
        output_dir=args.output,
        batch_size=args.batch_size,
        generate_master_file=args.master_file
    )

if __name__ == "__main__":
    main()